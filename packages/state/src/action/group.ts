import { 
    AITableGroupInfo, 
    ActionName, 
    SetViewAction,
    AITableView 
} from '@ai-table/utils';
import { AIViewTable } from '../types/ai-table';

/**
 * 设置视图分组
 */
function setViewGroup(aiTable: AIViewTable, viewId: string, groupInfo: AITableGroupInfo | null) {
    const operation: SetViewAction = {
        type: ActionName.SetView,
        path: [viewId],
        data: {
            settings: {
                ...aiTable.views().find(v => v._id === viewId)?.settings,
                groups: groupInfo || undefined,
                groupCollapse: [] // 重置折叠状态
            }
        }
    };
    aiTable.apply(operation);
}

/**
 * 设置分组折叠状态
 */
function setGroupCollapse(aiTable: AIViewTable, viewId: string, collapseState: string[]) {
    const view = aiTable.views().find(v => v._id === viewId);
    if (!view) return;

    const operation: SetViewAction = {
        type: ActionName.SetView,
        path: [viewId],
        data: {
            settings: {
                ...view.settings,
                groupCollapse: collapseState
            }
        }
    };
    aiTable.apply(operation);
}

/**
 * 切换单个分组的折叠状态
 */
function toggleGroupCollapse(aiTable: AIViewTable, viewId: string, groupId: string) {
    const view = aiTable.views().find(v => v._id === viewId);
    if (!view) return;

    const currentCollapse = view.settings?.groupCollapse || [];
    const newCollapse = currentCollapse.includes(groupId)
        ? currentCollapse.filter(id => id !== groupId)
        : [...currentCollapse, groupId];

    setGroupCollapse(aiTable, viewId, newCollapse);
}

/**
 * 添加分组字段
 */
function addGroupField(aiTable: AIViewTable, viewId: string, fieldId: string, desc: boolean = false) {
    const view = aiTable.views().find(v => v._id === viewId);
    if (!view) return;

    const currentGroups = view.settings?.groups || [];
    
    // 检查是否已存在
    if (currentGroups.some(group => group.fieldId === fieldId)) {
        throw new Error('字段已用于分组');
    }

    // 检查分组层级限制
    if (currentGroups.length >= 3) {
        throw new Error('最多支持3级分组');
    }

    const newGroups = [...currentGroups, { fieldId, desc }];
    setViewGroup(aiTable, viewId, newGroups);
}

/**
 * 移除分组字段
 */
function removeGroupField(aiTable: AIViewTable, viewId: string, fieldId: string) {
    const view = aiTable.views().find(v => v._id === viewId);
    if (!view) return;

    const currentGroups = view.settings?.groups || [];
    const newGroups = currentGroups.filter(group => group.fieldId !== fieldId);
    
    setViewGroup(aiTable, viewId, newGroups.length > 0 ? newGroups : null);
}

/**
 * 更新分组字段排序方向
 */
function updateGroupFieldDirection(aiTable: AIViewTable, viewId: string, fieldId: string, desc: boolean) {
    const view = aiTable.views().find(v => v._id === viewId);
    if (!view) return;

    const currentGroups = view.settings?.groups || [];
    const newGroups = currentGroups.map(group => 
        group.fieldId === fieldId ? { ...group, desc } : group
    );
    
    setViewGroup(aiTable, viewId, newGroups);
}

/**
 * 重新排序分组字段
 */
function reorderGroupFields(aiTable: AIViewTable, viewId: string, fromIndex: number, toIndex: number) {
    const view = aiTable.views().find(v => v._id === viewId);
    if (!view) return;

    const currentGroups = view.settings?.groups || [];
    
    if (fromIndex < 0 || fromIndex >= currentGroups.length ||
        toIndex < 0 || toIndex >= currentGroups.length) {
        return;
    }

    const newGroups = [...currentGroups];
    const [movedItem] = newGroups.splice(fromIndex, 1);
    newGroups.splice(toIndex, 0, movedItem);
    
    setViewGroup(aiTable, viewId, newGroups);
}

/**
 * 清除所有分组
 */
function clearAllGroups(aiTable: AIViewTable, viewId: string) {
    setViewGroup(aiTable, viewId, null);
}

export const GroupActions = {
    setViewGroup,
    setGroupCollapse,
    toggleGroupCollapse,
    addGroupField,
    removeGroupField,
    updateGroupFieldDirection,
    reorderGroupFields,
    clearAllGroups
};
