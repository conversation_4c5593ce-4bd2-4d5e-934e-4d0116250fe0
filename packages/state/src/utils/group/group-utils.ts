import { AITable, AITableQueries, FieldModelMap } from '@ai-table/grid';
import { 
    AITableGroupInfo, 
    AITableViewRecords, 
    AITableViewFields, 
    AITableView,
    AITableViewField,
    AITableViewRecord,
    Id 
} from '@ai-table/utils';
import { 
    AITableLinearRow, 
    AITableLinearRowGroupTab, 
    AITableRowType 
} from '@ai-table/grid';
import { GroupCalculator } from './group-calculator';

/**
 * 构建带分组的记录线性行结构
 */
export function buildGroupedLinearRows(
    records: AITableViewRecords,
    fields: AITableViewFields,
    activeView: AITableView,
    aiTable: AITable
): AITableLinearRow[] {
    const groupInfo = activeView.settings?.groups;
    const collapseState = activeView.settings?.groupCollapse;

    if (!groupInfo || groupInfo.length === 0) {
        // 没有分组时，使用原有的构建逻辑
        return buildNormalLinearRows(records);
    }

    const calculator = new GroupCalculator(groupInfo, aiTable, collapseState);
    return calculator.calculateLinearRows(records, fields);
}

/**
 * 构建普通线性行（无分组）
 */
function buildNormalLinearRows(records: AITableViewRecords): AITableLinearRow[] {
    const linearRows: AITableLinearRow[] = [];
    let displayRowIndex = 0;

    [...records, { _id: '' } as AITableViewRecord].forEach((record) => {
        if (record._id) {
            displayRowIndex++;
            linearRows.push({
                type: AITableRowType.record,
                _id: record._id,
                displayIndex: displayRowIndex
            });
        } else {
            linearRows.push({
                type: AITableRowType.add,
                _id: ''
            });
        }
    });

    return linearRows;
}

/**
 * 获取可用于分组的字段
 */
export function getGroupableFields(fields: AITableViewFields): AITableViewField[] {
    // 过滤出可以用于分组的字段类型
    const groupableFieldTypes = [
        'text',
        'number', 
        'select',
        'date',
        'member',
        'checkbox'
    ];

    return fields.filter(field => 
        groupableFieldTypes.includes(field.type) && 
        !field.name.startsWith('_') // 排除系统字段
    );
}

/**
 * 验证分组配置是否有效
 */
export function validateGroupInfo(
    groupInfo: AITableGroupInfo, 
    fields: AITableViewFields
): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const fieldIds = new Set(fields.map(f => f._id));
    const usedFieldIds = new Set<string>();

    // 检查分组层级数量
    if (groupInfo.length > 3) {
        errors.push('分组层级不能超过3级');
    }

    // 检查字段是否存在和重复
    groupInfo.forEach((group, index) => {
        if (!fieldIds.has(group.fieldId)) {
            errors.push(`第${index + 1}级分组字段不存在`);
        }

        if (usedFieldIds.has(group.fieldId)) {
            errors.push(`字段不能重复用于分组`);
        }

        usedFieldIds.add(group.fieldId);
    });

    // 检查字段类型是否支持分组
    const groupableFields = getGroupableFields(fields);
    const groupableFieldIds = new Set(groupableFields.map(f => f._id));

    groupInfo.forEach((group, index) => {
        if (!groupableFieldIds.has(group.fieldId)) {
            const field = fields.find(f => f._id === group.fieldId);
            errors.push(`第${index + 1}级分组字段"${field?.name || group.fieldId}"不支持分组`);
        }
    });

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * 生成默认分组配置
 */
export function createDefaultGroupInfo(fieldId: string): AITableGroupInfo {
    return [{
        fieldId,
        desc: false
    }];
}

/**
 * 添加分组字段
 */
export function addGroupField(
    currentGroupInfo: AITableGroupInfo, 
    fieldId: string, 
    desc: boolean = false
): AITableGroupInfo {
    if (currentGroupInfo.length >= 3) {
        throw new Error('最多支持3级分组');
    }

    if (currentGroupInfo.some(group => group.fieldId === fieldId)) {
        throw new Error('字段已用于分组');
    }

    return [...currentGroupInfo, { fieldId, desc }];
}

/**
 * 移除分组字段
 */
export function removeGroupField(
    currentGroupInfo: AITableGroupInfo, 
    fieldId: string
): AITableGroupInfo {
    return currentGroupInfo.filter(group => group.fieldId !== fieldId);
}

/**
 * 更新分组字段排序方向
 */
export function updateGroupFieldDirection(
    currentGroupInfo: AITableGroupInfo, 
    fieldId: string, 
    desc: boolean
): AITableGroupInfo {
    return currentGroupInfo.map(group => 
        group.fieldId === fieldId ? { ...group, desc } : group
    );
}

/**
 * 重新排序分组字段
 */
export function reorderGroupFields(
    currentGroupInfo: AITableGroupInfo, 
    fromIndex: number, 
    toIndex: number
): AITableGroupInfo {
    if (fromIndex < 0 || fromIndex >= currentGroupInfo.length ||
        toIndex < 0 || toIndex >= currentGroupInfo.length) {
        return currentGroupInfo;
    }

    const newGroupInfo = [...currentGroupInfo];
    const [movedItem] = newGroupInfo.splice(fromIndex, 1);
    newGroupInfo.splice(toIndex, 0, movedItem);
    
    return newGroupInfo;
}

/**
 * 获取分组字段的显示名称
 */
export function getGroupFieldDisplayName(
    fieldId: string, 
    fields: AITableViewFields
): string {
    const field = fields.find(f => f._id === fieldId);
    return field?.name || fieldId;
}

/**
 * 格式化分组值显示
 */
export function formatGroupValue(
    value: any, 
    field: AITableViewField, 
    aiTable: AITable
): string {
    if (value == null || value === '') {
        return '(空)';
    }

    const fieldModel = FieldModelMap[field.type];
    if (fieldModel && fieldModel.cellValueToString) {
        try {
            return fieldModel.cellValueToString(value, {
                aiTable,
                field
            });
        } catch (error) {
            console.warn('格式化分组值失败:', error);
        }
    }

    // 默认转换为字符串
    if (typeof value === 'object') {
        return JSON.stringify(value);
    }
    
    return String(value);
}

/**
 * 计算分组统计信息
 */
export function calculateGroupStats(
    groupTabRow: AITableLinearRowGroupTab,
    records: AITableViewRecords,
    fields: AITableViewFields,
    aiTable: AITable
): { recordCount: number; [key: string]: any } {
    // 基础统计信息
    const stats = {
        recordCount: groupTabRow.recordCount
    };

    // 可以扩展更多统计信息，如求和、平均值等
    // TODO: 实现更复杂的统计逻辑

    return stats;
}

/**
 * 检查记录是否匹配分组条件
 */
export function isRecordInGroup(
    record: AITableViewRecord,
    groupTabRow: AITableLinearRowGroupTab,
    fields: AITableViewFields,
    aiTable: AITable
): boolean {
    const field = fields.find(f => f._id === groupTabRow.fieldId);
    if (!field) return false;

    const recordValue = AITableQueries.getFieldValue(aiTable, [record._id, field._id]);
    const fieldModel = FieldModelMap[field.type];
    
    if (!fieldModel) return false;

    const compareResult = fieldModel.compare(
        recordValue,
        groupTabRow.groupValue,
        aiTable.context?.references?.() || {},
        undefined,
        { aiTable, field }
    );

    return compareResult === 0;
}
