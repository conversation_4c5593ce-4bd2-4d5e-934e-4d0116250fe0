import { AITable, AITableQueries, FieldModelMap } from '@ai-table/grid';
import { AITableGroupInfo, AITableViewRecords, AITableViewFields, AITableViewField, AITableViewRecord, Id } from '@ai-table/utils';
import { AITableLinearRow, AITableLinearRowGroupTab, AITableLinearRowBlank, AITableLinearRowRecord, AITableRowType } from '@ai-table/grid';
import { nanoid } from 'nanoid';

/**
 * 分组计算器 - 负责计算分组结构和生成线性行
 */
export class GroupCalculator {
    private groupInfo: AITableGroupInfo;
    private groupBreakpoints: Map<string, number[]>;
    private groupCollapseState: Set<string>;
    private aiTable: AITable;

    constructor(groupInfo: AITableGroupInfo, aiTable: AITable, collapseState?: string[]) {
        this.groupInfo = groupInfo;
        this.groupBreakpoints = new Map();
        this.groupCollapseState = new Set(collapseState || []);
        this.aiTable = aiTable;
    }

    /**
     * 计算分组线性行结构
     */
    calculateLinearRows(records: AITableViewRecords, fields: AITableViewFields): AITableLinearRow[] {
        if (!this.groupInfo || this.groupInfo.length === 0) {
            // 没有分组时，返回原始记录行
            return this.buildNormalLinearRows(records);
        }

        // 1. 按分组字段排序记录
        const sortedRecords = this.sortRecordsByGroup(records, fields);

        // 2. 检测分组断点
        this.detectGroupBreakpoints(sortedRecords, fields);

        // 3. 生成线性行结构
        return this.generateLinearRows(sortedRecords, fields);
    }

    /**
     * 构建普通线性行（无分组）
     */
    private buildNormalLinearRows(records: AITableViewRecords): AITableLinearRow[] {
        const linearRows: AITableLinearRow[] = [];
        let displayRowIndex = 0;

        [...records, { _id: '' } as AITableViewRecord].forEach((record) => {
            if (record._id) {
                displayRowIndex++;
                linearRows.push({
                    type: AITableRowType.record,
                    _id: record._id,
                    displayIndex: displayRowIndex
                } as AITableLinearRowRecord);
            } else {
                // 添加行
                linearRows.push({
                    type: AITableRowType.add,
                    _id: ''
                });
            }
        });

        return linearRows;
    }

    /**
     * 按分组字段排序记录
     */
    private sortRecordsByGroup(records: AITableViewRecords, fields: AITableViewFields): AITableViewRecords {
        const fieldsMap = new Map(fields.map((field) => [field._id, field]));

        return [...records].sort((record1, record2) => {
            return (
                this.groupInfo.reduce((result, groupField, index) => {
                    if (result !== 0) return result;

                    const field = fieldsMap.get(groupField.fieldId);
                    if (!field) return 0;

                    const value1 = AITableQueries.getFieldValue(this.aiTable, [record1._id, field._id]);
                    const value2 = AITableQueries.getFieldValue(this.aiTable, [record2._id, field._id]);

                    const fieldModel = FieldModelMap[field.type];
                    if (!fieldModel) return 0;

                    const compareResult = fieldModel.compare(value1, value2, this.aiTable.context?.references?.() || {}, undefined, {
                        aiTable: this.aiTable,
                        field
                    });

                    return compareResult * (groupField.desc ? -1 : 1);
                }, 0) || 1
            );
        });
    }

    /**
     * 检测分组断点
     */
    private detectGroupBreakpoints(records: AITableViewRecords, fields: AITableViewFields): void {
        this.groupBreakpoints.clear();

        if (records.length === 0) return;

        const fieldsMap = new Map(fields.map((field) => [field._id, field]));
        let previousRecord: AITableViewRecord | null = null;

        records.forEach((record, index) => {
            if (previousRecord === null) {
                // 第一条记录，所有分组字段都是断点
                this.groupInfo.forEach((groupField, groupIndex) => {
                    this.addBreakpoint(groupField.fieldId, index, groupIndex);
                });
            } else {
                // 检查每个分组字段是否发生变化
                this.groupInfo.forEach((groupField, groupIndex) => {
                    const field = fieldsMap.get(groupField.fieldId);
                    if (!field) return;

                    const prevValue = AITableQueries.getFieldValue(this.aiTable, [previousRecord!._id, field._id]);
                    const currValue = AITableQueries.getFieldValue(this.aiTable, [record._id, field._id]);

                    const fieldModel = FieldModelMap[field.type];
                    if (!fieldModel) return;

                    const compareResult = fieldModel.compare(prevValue, currValue, this.aiTable.context?.references?.() || {}, undefined, {
                        aiTable: this.aiTable,
                        field
                    });

                    if (compareResult !== 0) {
                        // 值发生变化，从当前层级开始的所有层级都是断点
                        for (let i = groupIndex; i < this.groupInfo.length; i++) {
                            this.addBreakpoint(this.groupInfo[i].fieldId, index, i);
                        }
                        return; // 找到断点后跳出循环
                    }
                });
            }

            previousRecord = record;
        });
    }

    /**
     * 添加断点
     */
    private addBreakpoint(fieldId: string, recordIndex: number, depth: number): void {
        if (!this.groupBreakpoints.has(fieldId)) {
            this.groupBreakpoints.set(fieldId, []);
        }
        this.groupBreakpoints.get(fieldId)!.push(recordIndex);
    }

    /**
     * 生成线性行结构
     */
    private generateLinearRows(records: AITableViewRecords, fields: AITableViewFields): AITableLinearRow[] {
        const linearRows: AITableLinearRow[] = [];
        const fieldsMap = new Map(fields.map((field) => [field._id, field]));
        let displayRowIndex = 0;

        // 添加空白行开始
        linearRows.push({
            type: AITableRowType.blank,
            _id: nanoid(),
            depth: 0
        } as AITableLinearRowBlank);

        records.forEach((record, index) => {
            // 检查是否需要生成分组标签行
            const groupTabRows = this.generateGroupTabRows(record, index, fields, fieldsMap);
            linearRows.push(...groupTabRows);

            // 检查当前记录是否应该显示（不在折叠的分组中）
            if (this.shouldShowRecord(record, index)) {
                displayRowIndex++;
                linearRows.push({
                    type: AITableRowType.record,
                    _id: record._id,
                    displayIndex: displayRowIndex
                } as AITableLinearRowRecord);
            }
        });

        // 添加新增行（如果最后一个分组没有折叠）
        if (this.shouldShowAddRow()) {
            linearRows.push({
                type: AITableRowType.add,
                _id: ''
            });
        }

        return linearRows;
    }

    /**
     * 生成分组标签行
     */
    private generateGroupTabRows(
        record: AITableViewRecord,
        recordIndex: number,
        fields: AITableViewFields,
        fieldsMap: Map<string, AITableViewField>
    ): AITableLinearRowGroupTab[] {
        const groupTabRows: AITableLinearRowGroupTab[] = [];

        this.groupInfo.forEach((groupField, depth) => {
            const breakpoints = this.groupBreakpoints.get(groupField.fieldId) || [];

            if (breakpoints.includes(recordIndex)) {
                const field = fieldsMap.get(groupField.fieldId);
                if (!field) return;

                const groupValue = AITableQueries.getFieldValue(this.aiTable, [record._id, field._id]);
                const groupId = this.generateGroupId(groupField.fieldId, groupValue, depth);
                const recordCount = this.calculateGroupRecordCount(record, recordIndex, depth);

                groupTabRows.push({
                    type: AITableRowType.groupTab,
                    _id: nanoid(),
                    depth,
                    fieldId: groupField.fieldId,
                    groupValue,
                    isCollapsed: this.groupCollapseState.has(groupId),
                    recordCount,
                    groupId
                } as AITableLinearRowGroupTab);
            }
        });

        return groupTabRows;
    }

    /**
     * 生成分组ID
     */
    private generateGroupId(fieldId: string, groupValue: any, depth: number): string {
        return `${fieldId}_${depth}_${JSON.stringify(groupValue)}`;
    }

    /**
     * 计算分组包含的记录数量
     */
    private calculateGroupRecordCount(record: AITableViewRecord, recordIndex: number, depth: number): number {
        // TODO: 实现精确的记录数量计算
        // 这里需要计算从当前断点到下一个同级分组断点之间的记录数
        return 1;
    }

    /**
     * 判断记录是否应该显示
     */
    private shouldShowRecord(record: AITableViewRecord, recordIndex: number): boolean {
        // TODO: 实现折叠状态检查
        // 需要检查记录所在的所有父级分组是否都处于展开状态
        return true;
    }

    /**
     * 判断是否应该显示添加行
     */
    private shouldShowAddRow(): boolean {
        // TODO: 实现添加行显示逻辑
        // 只有在最后一个分组展开时才显示添加行
        return true;
    }

    /**
     * 切换分组折叠状态
     */
    toggleGroupCollapse(groupId: string): void {
        if (this.groupCollapseState.has(groupId)) {
            this.groupCollapseState.delete(groupId);
        } else {
            this.groupCollapseState.add(groupId);
        }
    }

    /**
     * 获取当前折叠状态
     */
    getCollapseState(): string[] {
        return Array.from(this.groupCollapseState);
    }
}
