import { ElementRef, ViewContainerRef } from '@angular/core';
import { AITable, Coordinate } from '../core';
import { ThyNotifyService } from 'ngx-tethys/notify';

export enum AITableRowType {
    add = 'add',
    record = 'record',
    groupTab = 'groupTab', // 分组标签行
    blank = 'blank' // 空白行
}

export type AITableCellMetaData = {
    size: number;
    offset: number;
};

export type AITableCellMetaDataMap = Record<number, AITableCellMetaData>;

export type AITableLinearRowAdd = {
    _id: string;
    type: AITableRowType.add;
};

export type AITableLinearRowRecord = {
    _id: string;
    type: AITableRowType.record;
    displayIndex: number;
};

export type AITableLinearRowGroupTab = {
    _id: string;
    type: AITableRowType.groupTab;
    depth: number; // 分组层级深度 (0-2)
    fieldId: string; // 分组字段ID
    groupValue: any; // 分组值
    isCollapsed: boolean; // 是否折叠
    recordCount: number; // 包含的记录数量
    groupId: string; // 分组唯一标识
};

export type AITableLinearRowBlank = {
    _id: string;
    type: AITableRowType.blank;
    depth: number;
};

export type AITableLinearRow = AITableLinearRowAdd | AITableLinearRowRecord | AITableLinearRowGroupTab | AITableLinearRowBlank;

export interface AITableRowHeadsConfig {
    coordinate: Coordinate;
    rowStartIndex: number;
    rowStopIndex: number;
    aiTable: AITable;
    readonly: boolean;
    maxRecords?: number;
}

export interface AITableContextMenuItem {
    type: string;
    name?: string;
    icon?: string;
    shortcutKey?: string;
    isInputNumber?: boolean;
    nameSuffix?: string;
    count?: number;
    exec?: (
        aiTable: AITable,
        targetName: string,
        position: { x: number; y: number },
        notifyService: ThyNotifyService,
        moduleValue?: any
    ) => void;
    hidden?: (aiTable: AITable, targetName: string, position: { x: number; y: number }) => boolean;
    disabled?: (aiTable: AITable, targetName: string, position: { x: number; y: number }) => boolean;
}

export interface AITableContextMenuOptions {
    origin: ElementRef<any> | HTMLElement;
    position: { x: number; y: number };
    menuItems: AITableContextMenuItem[];
    targetName: string;
    viewContainerRef: ViewContainerRef;
}
