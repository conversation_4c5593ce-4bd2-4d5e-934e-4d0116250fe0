import { AITable, getFieldOptions } from '../core';
import { AITableGridData, AITableLinearRow } from '../types';
import { AITableRowType } from '../types/row';
import { AITableFields, AITableRecords, AITableView, AITableViewFields, AITableViewRecords } from '@ai-table/utils';

export const buildGridLinearRows = (
    visibleRecords: AITableRecords,
    isAddingVisible: boolean = true,
    activeView?: AITableView,
    fields?: AITableViewFields,
    aiTable?: AITable
): AITableLinearRow[] => {
    // 如果有分组信息且提供了必要参数，使用分组构建逻辑
    if (activeView?.settings?.groups && fields && aiTable) {
        // 动态导入分组工具函数以避免循环依赖
        try {
            // 这里需要在运行时导入，避免编译时的循环依赖
            const { buildGroupedLinearRows } = require('@ai-table/state');
            return buildGroupedLinearRows(visibleRecords as AITableViewRecords, fields, activeView, aiTable);
        } catch (error) {
            console.warn('分组功能加载失败，使用默认构建方式:', error);
        }
    }

    // 默认构建逻辑（无分组）
    const linearRows: AITableLinearRow[] = [];
    let displayRowIndex = 0;
    [...visibleRecords, { _id: '' }].forEach((row) => {
        if (row._id) {
            displayRowIndex++;
            linearRows.push({
                type: AITableRowType.record,
                _id: row._id,
                displayIndex: displayRowIndex
            });
        }
        if (isAddingVisible && !row._id) {
            linearRows.push({
                type: AITableRowType.add,
                _id: ''
            });
        }
    });
    return linearRows;
};

export const buildGridData = (aiTable: AITable, recordValue: AITableRecords, fieldsValue: AITableFields): AITableGridData => {
    const fieldOptions = getFieldOptions(aiTable);
    const fields = fieldsValue.map((value) => {
        const fieldOption = fieldOptions.find((item) => item.type === value.type)!;
        return {
            ...value,
            icon: value.icon || fieldOption.icon
        };
    });
    return {
        type: 'grid',
        fields,
        records: recordValue
    };
};
