export const AI_TABLE_ACTION_COMMON_SIZE = 32; // 表格图标action背景的通用尺寸
export const AI_TABLE_ACTION_COMMON_RADIUS = 4; // 表格图标action背景的radius通用尺寸
export const AI_TABLE_ACTION_COMMON_RIGHT_PADDING = 6; // 表格图标action右侧padding尺寸
export const AI_TABLE_DEFAULT_COLUMN_WIDTH = 200; // 默认列宽
export const AI_TABLE_SCROLL_BAR_PADDING = 3; // 单元格滑动容器的滚动条宽度
export const AI_TABLE_OFFSET = 0.5; // 边框线偏移值
export const AI_TABLE_TEXT_GAP = 8; // 文本间距

export const AI_TABLE_ROW_HEAD = 'AI_TABLE_ROW_HEAD'; // 行头
export const AI_TABLE_ROW_DRAG_ICON_WIDTH = 18; // 行拖拽宽度
export const AI_TABLE_ROW_HEAD_WIDTH = 44; // 表格行头 checkbox 列的宽度
export const AI_TABLE_ROW_HEAD_WIDTH_AND_DRAG_ICON_WIDTH = AI_TABLE_ROW_HEAD_WIDTH + AI_TABLE_ROW_DRAG_ICON_WIDTH;
export const AI_TABLE_ROW_HEAD_SIZE = 16; // 添加行按钮的尺寸
export const AI_TABLE_ROW_ADD_BUTTON = 'AI_TABLE_ROW_ADD_BUTTON'; // 添加行名称
export const AI_TABLE_BLANK = 'AI_TABLE_BLANK'; // 空白区域

export const AI_TABLE_INDEX_FIELD_TEXT = ''; // 索引列显示文本
export const AI_TABLE_FIELD_HEAD = 'AI_TABLE_FIELD_HEAD'; // 属性列头标识
export const AI_TABLE_FIELD_HEAD_HEIGHT = 44; // 表格字段列头的高度
export const AI_TABLE_ROW_BLANK_HEIGHT = 43; // 减去边框后真实的行高
export const AI_TABLE_ROW_HEIGHT = 44; // 默认行高基准
export const AI_TABLE_CELL_ACTIVE_BORDER_WIDTH = 2; // 选中单元格的边框宽度
export const AI_TABLE_CELL_ATTACHMENT_ADD = 'AI_TABLE_CELL_ATTACHMENT_ADD'; // 附件cell中新增图标名称
export const AI_TABLE_CELL_ATTACHMENT_FILE = 'AI_TABLE_CELL_ATTACHMENT_FILE'; // 附件cell中文件
export const AI_TABLE_CELL_EDIT = 'AI_TABLE_CELL_EDIT'; // 附件cell中编辑图标名称
export const AI_TABLE_FIELD_HEAD_TEXT_MIN_WIDTH = 30; // 字段列头文本的最小宽度
export const AI_TABLE_ROW_SELECT_CHECKBOX = 'AI_TABLE_ROW_SELECT_CHECKBOX'; // 行 checkbox
export const AI_TABLE_FIELD_HEAD_SELECT_CHECKBOX = 'AI_TABLE_FIELD_HEAD_SELECT_CHECKBOX'; // 表头 checkbox 标识
export const AI_TABLE_FIELD_ADD_BUTTON = 'AI_TABLE_FIELD_ADD_BUTTON'; // 添加列名称
export const AI_TABLE_FIELD_ADD_BUTTON_WIDTH = 100; // 添加列宽度
export const AI_TABLE_FIELD_HEAD_ICON_GAP_SIZE = 8; // 字段表列头图标的间距
export const AI_TABLE_FIELD_HEAD_MORE = 'AI_TABLE_FIELD_HEAD_MORE'; // 更多图标名称
export const AI_TABLE_FIELD_HEAD_OPACITY_LINE = 'AI_TABLE_FIELD_HEAD_OPACITY_LINE'; // 字段列头透明线
export const AI_TABLE_FIELD_STAT_BG = 'AI_TABLE_FIELD_STAT_BG'; // 统计按钮背景
export const AI_TABLE_ROW_DRAG = 'AI_TABLE_ROW_DRAG'; // 行拖拽
export const AI_TABLE_FILL_HANDLE = 'AI_TABLE_FILL_HANDLE'; // 填充手柄

export const AI_TABLE_PREVENT_CLEAR_SELECTION_CLASS = '.ai-table-prevent-clear-selection';

export const AI_TABLE_ICON_COMMON_SIZE = 16; // 表格图标的通用尺寸
export const AI_TABLE_CELL = 'AI_TABLE_CELL'; // 单元格标识
// 因为 dom 的边距 12 是不包含 边框的，所以加上边框 2px  才能跟 编辑里面的内容对其；
export const AI_TABLE_CELL_PADDING = 14; // 单元格内容的内边距
export const AI_TABLE_CELL_BORDER = 2; // 单元格边框宽度
export const AI_TABLE_CELL_LINE_BORDER = 1; // 单元格线宽度
export const AI_TABLE_CELL_MULTI_PADDING_TOP = 10;
export const AI_TABLE_CELL_MULTI_ITEM_MARGIN_TOP = 4;
export const AI_TABLE_CELL_MULTI_PADDING_LEFT = 4;
// 最少显示文字宽度，一个文字+ 三个点
export const AI_TABLE_MIN_TEXT_WIDTH = 24;
export const AI_TABLE_CELL_MULTI_DOT_RADIUS = 4;
export const AI_TABLE_CELL_MULTI_ITEM_MARGIN_LEFT = 8;
export const AI_TABLE_CELL_DELETE_ITEM_BUTTON_SIZE = 8; // 选项删除按钮的宽度
export const AI_TABLE_CELL_DELETE_ITEM_BUTTON_SIZE_OFFSET = 12; // 选项字段删除按钮大小的偏移量
export const AI_TABLE_CELL_MULTI_ITEM_MIN_WIDTH = 36; // 选项字段项的最小宽度
export const AI_TABLE_CELL_MAX_ROW_COUNT = 1; // 默认单元格展示的最大行数
export const AI_TABLE_CELL_EMOJI_SIZE = 18;
export const AI_TABLE_CELL_EMOJI_PADDING = 8;
export const AI_TABLE_CELL_MEMBER_MAX_HEIGHT = 130; // 成员字段项最大高度
export const AI_TABLE_CELL_MEMBER_ITEM_HEIGHT = 24; // 成员字段项高度
export const AI_TABLE_CELL_MEMBER_ITEM_PADDING = 4; // 多个成员头像间距
export const AI_TABLE_CELL_ADD_ITEM_BUTTON_SIZE = 22; // 成员/选项/链接/附件字段的新按钮大小

export const AI_TABLE_MEMBER_AVATAR_SIZE = 24; // 成员头像大小
export const AI_TABLE_MEMBER_ITEM_PADDING_RIGHT = 8; // 成员字段项右边距
export const AI_TABLE_MEMBER_ITEM_AVATAR_MARGIN_RIGHT = 8; // 成员头像与成员名之间的间距

export const AI_TABLE_FILE_ICON_ITEM_HEIGHT = 20; // 文件字段项高度
export const AI_TABLE_FILE_ICON_SIZE = 20; // 文件图标大小
export const AI_TABLE_CELL_FIELD_ITEM_HEIGHT = 8; // 文件字段项右边距
export const AI_TABLE_FIELD_ITEM_MARGIN_RIGHT = 8; // 文件图标之间的间距

export const AI_TABLE_OPTION_ITEM_PADDING = 10; // 选项按钮间距
export const AI_TABLE_OPTION_ITEM_HEIGHT = 24;
export const AI_TABLE_OPTION_ITEM_FONT_SIZE = 14;
export const AI_TABLE_OPTION_MULTI_ITEM_FONT_SIZE = 12;
export const AI_TABLE_OPTION_ITEM_RADIUS = 18;
export const AI_TABLE_TAG_PADDING = 12;
export const AI_TABLE_TAG_FONT_SIZE = 12;

export const AI_TABLE_PIECE_WIDTH = 10;
export const AI_TABLE_PIECE_RADIUS = 4;
export const AI_TABLE_COMMON_FONT_SIZE = 14;
export const AI_TABLE_DOT_RADIUS = 5;

export const AI_TABLE_PROGRESS_BAR_HEIGHT = 10;
export const AI_TABLE_PROGRESS_BAR_RADIUS = 5;
export const AI_TABLE_PROGRESS_BAR_POINTER_HEIGHT = 18;
export const AI_TABLE_PROGRESS_BAR_POINTER_WIDTH = 8;
export const AI_TABLE_PROGRESS_TEXT_WIDTH = 46;
export const AI_TABLE_POPOVER_LEFT_OFFSET = 4;

export const AI_TABLE_RATE_MAX = 5;

export const AI_TABLE_SCROLL_BAR_SIZE = 18;
export const AI_TABLE_AUTO_SCROLL_LEFT_THRESHOLD = 40;
export const AI_TABLE_AUTO_SCROLL_RIGHT_THRESHOLD = 40;
export const AI_TABLE_AUTO_SCROLL_TOP_THRESHOLD = AI_TABLE_FIELD_HEAD_HEIGHT / 2;
export const AI_TABLE_AUTO_SCROLL_BOTTOM_THRESHOLD = AI_TABLE_FIELD_HEAD_HEIGHT / 2;

export const AI_TABLE_FIELD_STAT_INNER_HEIGHT = 47; // 字段统计内部高度
export const AI_TABLE_TEXT_LINE_HEIGHT = 1.84; // 默认文本行高
export const AI_TABLE_FIELD_STAT_CONTAINER_HEIGHT = AI_TABLE_FIELD_STAT_INNER_HEIGHT + AI_TABLE_OFFSET * 2; // 统计容器高度
