/**
 * 分组相关常量定义
 */

// 分组层级缩进宽度
export const GROUP_INDENT_WIDTH = 24;

// 分组标签高度
export const GROUP_TAB_HEIGHT = 32;

// 空白行高度
export const BLANK_ROW_HEIGHT = 8;

// 最大分组层级数
export const MAX_GROUP_LEVELS = 3;

// 分组展开/折叠图标大小
export const GROUP_EXPAND_ICON_SIZE = 16;

// 分组标签背景色（按层级）
export const GROUP_TAB_COLORS = [
    '#f8f9fa', // 第一级
    '#f1f3f4', // 第二级
    '#e8eaed'  // 第三级
];

// 分组标签文字颜色
export const GROUP_TAB_TEXT_COLOR = '#5f6368';

// 分组标签边框颜色
export const GROUP_TAB_BORDER_COLOR = '#dadce0';

// 支持分组的字段类型
export const GROUPABLE_FIELD_TYPES = [
    'text',
    'number',
    'select',
    'date',
    'member',
    'checkbox'
];

// 分组值为空时的显示文本
export const EMPTY_GROUP_VALUE_TEXT = '(空)';

// 分组折叠状态存储键前缀
export const GROUP_COLLAPSE_STORAGE_PREFIX = 'ai_table_group_collapse_';
