<div class="d-flex align-items-center">
    <input thyInput [(ngModel)]="room" [disabled]="!!tableService.provider" placeholder="请输入房间号" style="width: 300px" />
    <a
        thyAction
        class="ml-2"
        [thyIcon]="tableService.provider ? 'unlink-insert' : 'link-insert'"
        href="javascript:;"
        (click)="handleShared()"
        >{{ tableService.provider ? '断开连接' : '连接协同' }}</a
    >
    <label class="mb-0 ml-2 d-flex align-items-center"
        ><input type="checkbox" [checked]="readonly" (change)="handleReadonlyChange($event)" /><span class="pl-1 pt-1">只读</span></label
    >
    <label class="mb-0 ml-2 d-flex align-items-center"
        ><input type="checkbox" [checked]="hiddenIndexColumn" (change)="handleHiddenIndexColumnChange($event)" /><span class="pl-1 pt-1"
            >隐藏索引列</span
        ></label
    >
    <label class="mb-0 ml-2 d-flex align-items-center"
        ><input type="checkbox" [checked]="hiddenRowDrag" (change)="handleHiddenRowDragChange($event)" /><span class="pl-1 pt-1"
            >隐藏行拖拽</span
        ></label
    >
    <label class="mb-0 ml-2 d-flex align-items-center"
        ><span class="pr-1 pt-1">最大列数</span
        ><input type="number" thyInput style="width: 50px" [(ngModel)]="maxFields" (change)="handleMaxFieldsChange()"
    /></label>
    <label class="mb-0 ml-2 d-flex align-items-center"
        ><span class="pr-1 pt-1">最大行数</span
        ><input type="number" thyInput style="width: 50px" [(ngModel)]="maxRecords" (change)="handleMaxRecordsChange()"
    /></label>
</div>
<thy-tabs [thyExtra]="extra" (thyActiveTabChange)="activeTabChange($event)" [thyActiveTab]="tableService.activeViewId()">
    @for (item of tableService.sortedViews(); let index = $index; track $index) {
        <thy-tab [thyTitle]="item.name" [id]="item._id">
            @if (item._id === tableService.activeViewId()) {
                <ng-template #title>
                    @if (isEdit) {
                        <input
                            thyAutofocus
                            thyInput
                            [ngModel]="item.name"
                            (thyEnter)="updateValue()"
                            (blur)="updateValue()"
                            (ngModelChange)="nameChange($event)"
                        />
                    } @else {
                        <p class="mb-0">
                            <span> {{ item.name }} </span>
                            @if (!readonly) {
                                <a thySize="xxs" thyAction class="ml-1" thyIcon="more" [thyDropdown]="menu" href="javascript:;"></a>
                            }
                        </p>
                    }
                </ng-template>
                <router-outlet></router-outlet>
            } @else {
                <ng-template #title>
                    <span> {{ item.name }} </span>
                </ng-template>
            }
        </thy-tab>
    }
</thy-tabs>

<thy-dropdown-menu #menu>
    <a thyDropdownMenuItem href="javascript:;" (click)="updateEditStatus()">
        <span>重命名</span>
    </a>
    <a thyDropdownMenuItem href="javascript:;" (click)="addView('duplicate')">
        <span>复制</span>
    </a>
    <a thyDropdownMenuItem class="remove-icon" href="javascript:;" (click)="removeView()">
        <span>删除</span>
    </a>
</thy-dropdown-menu>

<ng-template #extra>
    @if (!readonly) {
        <thy-action thyIcon="plus" class="cursor-pointer" (click)="addView('add')"></thy-action>
    }
</ng-template>
