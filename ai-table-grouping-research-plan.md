# AI-Table 表格分组功能实现调研方案

## 1. 项目概况分析

### 1.1 当前项目架构
基于对项目代码的分析，当前AI-Table项目采用以下技术架构：

- **前端框架**: Angular 19 + TypeScript
- **渲染引擎**: Konva.js (Canvas渲染) + DOM混合渲染
- **状态管理**: 基于Immer的自定义状态管理系统
- **协同编辑**: Yjs + WebSocket
- **包管理**: Monorepo架构，包含grid、state、utils三个核心包

### 1.2 核心模块结构
```
packages/
├── grid/           # 表格渲染和交互核心
├── state/          # 状态管理和数据同步
└── utils/          # 工具类和类型定义
```

### 1.3 数据流架构
```
数据源 → State管理 → 视图计算 → 线性行结构 → Konva渲染 → 用户界面
```

## 2. 分组功能需求分析

### 2.1 功能需求
参考APITable的实现，需要支持以下核心功能：

1. **多级分组**: 支持最多3级分组
2. **字段类型兼容**: 支持文本、数字、选项、日期等字段类型分组
3. **展开/折叠**: 分组标签支持展开和折叠操作
4. **拖拽排序**: 支持分组字段的拖拽重新排序
5. **实时计算**: 数据变化时自动重新计算分组结构
6. **状态持久化**: 分组配置和展开状态的本地存储

### 2.2 技术需求
1. **性能优化**: 大数据量下的虚拟化渲染
2. **交互体验**: 流畅的展开/折叠动画
3. **扩展性**: 便于后续添加新的分组功能

## 3. 技术实现方案

### 3.1 数据结构设计

#### 3.1.1 分组信息类型定义
```typescript
// packages/utils/src/types/view.ts
export interface AITableGroupField {
    fieldId: string;    // 分组字段ID
    desc: boolean;      // 是否降序排列
}

export type AITableGroupInfo = AITableGroupField[];

// 扩展视图设置类型
export interface AITableGroupOptions {
    groups?: AITableGroupInfo;
    groupCollapse?: string[];  // 折叠的分组ID列表
}

export type ViewSettings = AITableSearchOptions & 
                          AITableFilterConditions & 
                          AITableSortOptions & 
                          AITableFrozenOptions &
                          AITableGroupOptions;
```

#### 3.1.2 线性行结构扩展
```typescript
// packages/grid/src/types/row.ts
export enum AITableRowType {
    add = 'add',
    record = 'record',
    groupTab = 'groupTab',    // 新增：分组标签行
    blank = 'blank'           // 新增：空白行
}

export type AITableLinearRowGroupTab = {
    _id: string;
    type: AITableRowType.groupTab;
    depth: number;            // 分组层级深度 (0-2)
    fieldId: string;          // 分组字段ID
    groupValue: any;          // 分组值
    isCollapsed: boolean;     // 是否折叠
    recordCount: number;      // 包含的记录数量
};

export type AITableLinearRowBlank = {
    _id: string;
    type: AITableRowType.blank;
    depth: number;
};

export type AITableLinearRow = AITableLinearRowAdd | 
                              AITableLinearRowRecord | 
                              AITableLinearRowGroupTab | 
                              AITableLinearRowBlank;
```

### 3.2 分组核心算法

#### 3.2.1 分组计算类
```typescript
// packages/state/src/utils/group/group-calculator.ts
export class GroupCalculator {
    private groupInfo: AITableGroupInfo;
    private groupBreakpoints: Map<string, number[]>;
    private groupCollapseState: Set<string>;

    constructor(groupInfo: AITableGroupInfo, collapseState?: string[]) {
        this.groupInfo = groupInfo;
        this.groupBreakpoints = new Map();
        this.groupCollapseState = new Set(collapseState || []);
    }

    // 计算分组线性行结构
    calculateLinearRows(records: AITableViewRecords, fields: AITableViewFields): AITableLinearRow[] {
        // 实现分组计算逻辑
    }

    // 检测分组断点
    private detectGroupBreakpoints(records: AITableViewRecords, fields: AITableViewFields): void {
        // 实现断点检测逻辑
    }

    // 生成分组标签行
    private generateGroupTabRows(breakpointIndex: number, record: AITableViewRecord): AITableLinearRow[] {
        // 实现分组标签生成逻辑
    }
}
```

#### 3.2.2 分组排序算法
```typescript
// packages/state/src/utils/group/group-sorter.ts
export function sortRecordsByGroup(
    records: AITableViewRecords,
    groupInfo: AITableGroupInfo,
    fields: AITableViewFields,
    aiTable: AITable
): AITableViewRecords {
    return records.sort((record1, record2) => {
        return groupInfo.reduce((result, groupField, index) => {
            if (result !== 0) return result;
            
            const field = fields.find(f => f._id === groupField.fieldId);
            if (!field) return 0;
            
            const value1 = AITableQueries.getFieldValue(aiTable, [record1._id, field._id]);
            const value2 = AITableQueries.getFieldValue(aiTable, [record2._id, field._id]);
            
            const fieldModel = FieldModelMap[field.type];
            const compareResult = fieldModel.compare(value1, value2, aiTable.context!.references());
            
            return compareResult * (groupField.desc ? -1 : 1);
        }, 0) || 1;
    });
}
```

### 3.3 渲染系统扩展

#### 3.3.1 分组标签渲染器
```typescript
// packages/grid/src/renderer/drawers/group-tab-drawer.ts
export class GroupTabDrawer extends CellDrawer {
    draw(ctx: CanvasRenderingContext2D, row: AITableLinearRowGroupTab, config: any): void {
        const { depth, groupValue, isCollapsed, recordCount } = row;
        const groupOffset = depth * GROUP_INDENT_WIDTH;
        
        // 绘制分组背景
        this.drawGroupBackground(ctx, groupOffset, config);
        
        // 绘制展开/折叠图标
        this.drawExpandIcon(ctx, groupOffset, isCollapsed);
        
        // 绘制分组值
        this.drawGroupValue(ctx, groupOffset, groupValue, config);
        
        // 绘制记录数量
        this.drawRecordCount(ctx, recordCount, config);
    }
}
```

#### 3.3.2 渲染器组件扩展
```typescript
// packages/grid/src/renderer/components/other-rows/other-rows.component.ts
@Component({
    selector: 'ai-table-other-rows',
    template: `
        @for (row of otherRows(); track trackBy($index, row)) {
            @switch (row.type) {
                @case (AITableRowType.groupTab) {
                    <ko-group [config]="getGroupTabConfig(row)">
                        <ko-rect [config]="getGroupTabBgConfig(row)"></ko-rect>
                        <ko-text [config]="getGroupTabTextConfig(row)"></ko-text>
                        <ko-shape [config]="getExpandIconConfig(row)"></ko-shape>
                    </ko-group>
                }
                @case (AITableRowType.blank) {
                    <ko-rect [config]="getBlankRowConfig(row)"></ko-rect>
                }
            }
        }
    `
})
export class AITableOtherRows {
    // 实现分组行渲染逻辑
}
```

### 3.4 UI组件设计

#### 3.4.1 分组设置面板
```typescript
// src/app/components/group-panel/group-panel.component.ts
@Component({
    selector: 'ai-table-group-panel',
    template: `
        <div class="group-panel">
            <div class="group-header">
                <span>分组</span>
                <button (click)="addGroup()">添加分组</button>
            </div>
            
            <div class="group-list" cdkDropList (cdkDropListDropped)="onGroupReorder($event)">
                @for (group of groupInfo(); track group.fieldId; let i = $index) {
                    <div class="group-item" cdkDrag>
                        <select [(ngModel)]="group.fieldId" (change)="updateGroup(i, group)">
                            @for (field of availableFields(); track field._id) {
                                <option [value]="field._id">{{ field.name }}</option>
                            }
                        </select>
                        <button (click)="toggleSortDirection(i)">
                            {{ group.desc ? '降序' : '升序' }}
                        </button>
                        <button (click)="removeGroup(i)">删除</button>
                    </div>
                }
            </div>
        </div>
    `
})
export class GroupPanelComponent {
    // 实现分组设置逻辑
}
```

## 4. 实施计划

### 4.1 第一阶段：基础架构 (1-2周)
1. **扩展数据结构**: 添加分组相关的类型定义和接口
2. **核心算法实现**: 实现分组计算和排序算法
3. **线性行结构扩展**: 支持分组标签和空白行类型

### 4.2 第二阶段：渲染系统 (2-3周)
1. **分组标签渲染器**: 实现Canvas分组标签绘制
2. **渲染组件扩展**: 修改现有渲染组件支持分组行
3. **交互事件处理**: 实现展开/折叠交互

### 4.3 第三阶段：UI组件 (1-2周)
1. **分组设置面板**: 创建分组配置界面
2. **拖拽排序**: 实现分组字段拖拽重排
3. **状态持久化**: 实现分组配置的本地存储

### 4.4 第四阶段：集成优化 (1周)
1. **状态管理集成**: 将分组功能集成到现有状态系统
2. **性能优化**: 优化大数据量下的分组性能
3. **测试完善**: 编写单元测试和集成测试

## 5. 技术风险和挑战

### 5.1 主要挑战
1. **性能问题**: 大数据量下的分组计算和渲染性能
2. **状态同步**: 分组状态与协同编辑的同步
3. **渲染复杂度**: Canvas渲染中的分组层级和缩进处理

### 5.2 解决方案
1. **虚拟化渲染**: 只渲染可见区域的分组和记录
2. **增量计算**: 数据变化时只重新计算受影响的分组
3. **缓存机制**: 缓存分组计算结果，避免重复计算

## 6. 预期成果

### 6.1 功能成果
- 完整的表格分组功能，支持多级分组
- 流畅的用户交互体验
- 良好的性能表现

### 6.2 技术成果
- 可扩展的分组架构设计
- 完善的类型定义和接口
- 详细的技术文档和使用指南

## 7. 后续扩展

### 7.1 高级功能
- 分组统计和聚合计算
- 分组内排序和过滤
- 自定义分组规则

### 7.2 其他视图支持
- 看板视图分组
- 日历视图分组
- 甘特图分组

---

本调研方案基于对APITable开源项目的深入分析和当前AI-Table项目架构的理解，提供了完整的分组功能实现路径。建议按照分阶段的方式逐步实施，确保每个阶段的质量和稳定性。
